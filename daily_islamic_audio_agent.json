{"meta": {"instanceId": "your-instance-id"}, "name": "Daily Islamic Audio Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 0 * * *"}]}}, "id": "1", "name": "Daily Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"values": {"string": [{"name": "topic", "value": "Battle of Badr"}]}, "options": {}}, "id": "2", "name": "Set Topic", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"model": "gemini-pro", "prompt": "Write a conversation between a Muslim scholar and a curious student about the {{ $json.topic }}. Keep it educational, respectful, and suitable for audio narration.", "options": {}}, "id": "3", "name": "Generate Dialogue", "type": "n8n-nodes-langchain.googlegemini", "typeVersion": 1, "position": [670, 300], "credentials": {"googleGeminiApi": {"id": "your-gemini-credentials-id", "name": "Google Gemini API"}}}, {"parameters": {"operation": "textToSpeech", "voiceId": "YOUR_VOICE_ID", "text": "={{ $json.response }}", "voiceSettings": {"stability": 0.75, "similarityBoost": 0.75}, "options": {}}, "id": "4", "name": "Generate Audio", "type": "n8n-nodes-base.elevenlabs", "typeVersion": 1, "position": [900, 300], "credentials": {"elevenLabsApi": {"id": "your-elevenlabs-credentials-id", "name": "ElevenLabs API"}}}, {"parameters": {"resource": "message", "operation": "post", "webhookUrl": "YOUR_DISCORD_WEBHOOK_URL", "content": "🎙️ **Islamic Historical Talk: {{ $('Set Topic').item.json.topic }}**\n\nListen to a conversation between a scholar and a student about this important event in Islam's history.", "options": {}}, "id": "5", "name": "Send to Discord", "type": "n8n-nodes-base.discord", "typeVersion": 1, "position": [1130, 300]}], "connections": {"Daily Trigger": {"main": [[{"node": "Set Topic", "type": "main", "index": 0}]]}, "Set Topic": {"main": [[{"node": "Generate Dialogue", "type": "main", "index": 0}]]}, "Generate Dialogue": {"main": [[{"node": "Generate Audio", "type": "main", "index": 0}]]}, "Generate Audio": {"main": [[{"node": "Send to Discord", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-30T12:00:00.000Z", "versionId": "1"}